2025-07-23 00:44:51,081 - __main__ - INFO - Starting battery timeline analysis...
2025-07-23 00:44:51,081 - __main__ - INFO - Loading data files...
2025-07-23 00:44:51,081 - __main__ - INFO - Loading battery lifecycle timelines...
2025-07-23 00:44:51,138 - __main__ - INFO - Loaded 29206 timeline records
2025-07-23 00:44:51,138 - __main__ - INFO - Loading battery type data...
2025-07-23 00:44:51,151 - __main__ - INFO - Loaded 18027 battery type records
2025-07-23 00:44:51,152 - __main__ - INFO - Cleaning data...
2025-07-23 00:44:51,197 - __main__ - INFO - Removed 371 rows with invalid/zero-duration intervals
2025-07-23 00:44:51,197 - __main__ - INFO - Total removed: 374 rows (missing battery_id + invalid intervals)
2025-07-23 00:44:51,213 - __main__ - INFO - After cleaning: 28832 timeline records, 18026 battery type records
2025-07-23 00:44:51,213 - __main__ - INFO - Processing data for accure.csv output...
2025-07-23 00:44:51,246 - __main__ - INFO - Pre-indexing battery timelines for fast lookup...
2025-07-23 00:44:54,857 - __main__ - INFO - Processing 17989 unique batteries
2025-07-23 00:45:24,927 - __main__ - INFO - Processed 17989 battery records
2025-07-23 00:45:24,927 - __main__ - INFO - Batteries with type: 17989
2025-07-23 00:45:24,927 - __main__ - INFO - Batteries without type: 0
2025-07-23 00:45:24,929 - __main__ - INFO - Validating result...
2025-07-23 00:45:24,952 - __main__ - INFO - Generating accure.csv output...
2025-07-23 00:45:25,044 - __main__ - INFO - Saved 17989 records to accure.csv
2025-07-23 00:45:25,045 - __main__ - INFO - Saved statistics to analyze_battery_timeline_statistics.txt
2025-07-23 00:45:25,046 - __main__ - INFO - Analysis completed successfully!
2025-07-23 00:45:25,046 - __main__ - INFO - Output files: accure.csv, analyze_battery_timeline_statistics.txt
